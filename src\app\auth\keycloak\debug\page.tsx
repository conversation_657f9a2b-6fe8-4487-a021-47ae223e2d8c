'use client';

import { useState, useEffect } from 'react';
import { Box, Typography, Paper, Button, Alert, Divider } from '@mui/material';

import { CONFIG } from 'src/global-config';
import { useKeycloak } from 'src/auth/context/keycloak';

// ----------------------------------------------------------------------

export default function KeycloakDebugPage() {
  const [userInfo, setUserInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const { getUser, signOut } = useKeycloak();

  useEffect(() => {
    const loadUserInfo = async () => {
      try {
        const user = await getUser();
        setUserInfo(user);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      }
    };

    loadUserInfo();
  }, [getUser]);

  const handleLogout = async () => {
    try {
      await signOut();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Logout failed');
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Keycloak Debug Panel
      </Typography>

      {/* Configuration */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Cấu hình Keycloak
        </Typography>
        <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
          <div><strong>Authority:</strong> {CONFIG.keycloak.authority}</div>
          <div><strong>Client ID:</strong> {CONFIG.keycloak.clientId}</div>
          <div><strong>Redirect URI:</strong> {CONFIG.keycloak.redirectUri}</div>
          <div><strong>Post Logout Redirect URI:</strong> {CONFIG.keycloak.postLogoutRedirectUri}</div>
          <div><strong>Scope:</strong> {CONFIG.keycloak.scope}</div>
        </Box>
      </Paper>

      {/* Current URL */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Current URL Info
        </Typography>
        <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
          <div><strong>Origin:</strong> {typeof window !== 'undefined' ? window.location.origin : 'N/A'}</div>
          <div><strong>Full URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</div>
        </Box>
      </Paper>

      {/* User Info */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          User Information
        </Typography>
        {userInfo ? (
          <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
            <div><strong>Name:</strong> {userInfo.profile?.name}</div>
            <div><strong>Email:</strong> {userInfo.profile?.email}</div>
            <div><strong>Username:</strong> {userInfo.profile?.preferred_username}</div>
            <div><strong>Token Type:</strong> {userInfo.token_type}</div>
            <div><strong>Expires At:</strong> {new Date(userInfo.expires_at * 1000).toLocaleString()}</div>
          </Box>
        ) : (
          <Typography color="text.secondary">No user logged in</Typography>
        )}
      </Paper>

      {/* Error */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Actions */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Actions
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button variant="contained" color="error" onClick={handleLogout}>
            Test Logout
          </Button>
          <Button
            variant="outlined"
            onClick={() => window.open('/auth/keycloak/config-check', '_blank')}
          >
            Check Configuration
          </Button>
          <Button
            variant="outlined"
            onClick={() => window.open('http://localhost:8080/admin', '_blank')}
          >
            Keycloak Admin
          </Button>
        </Box>
      </Paper>

      {/* Troubleshooting */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Hướng dẫn khắc phục lỗi Logout
        </Typography>
        <Typography variant="body2" paragraph>
          Nếu logout bị kẹt ở Keycloak, hãy kiểm tra:
        </Typography>
        <Box component="ol" sx={{ pl: 2 }}>
          <li>
            <Typography variant="body2">
              <strong>Valid Redirect URIs</strong> trong Keycloak client phải bao gồm:
              <br />
              <code>http://localhost:8082/auth/keycloak/logout-callback</code>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <strong>Web Origins</strong> trong Keycloak client phải bao gồm:
              <br />
              <code>http://localhost:8082</code>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <strong>Access Type</strong> phải là <code>public</code>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <strong>Standard Flow Enabled</strong> phải được bật
            </Typography>
          </li>
        </Box>
      </Paper>
    </Box>
  );
}
