# Khắc phục lỗi Keycloak Logout bị kẹt

## 🚨 Vấn đề
Khi logout từ ứng dụng, người dùng bị redirect đến Keycloak logout URL nhưng không được redirect về ứng dụng:
```
http://localhost:8080/realms/ttu/protocol/openid-connect/logout?id_token_hint=...&post_logout_redirect_uri=http%3A%2F%2Flocalhost%3A8082%2Fauth%2Fkeycloak%2Flogout-callback
```

## ✅ Giải pháp đã thực hiện

### 1. Sửa lỗi trong logout callback page
**File:** `src/app/auth/keycloak/logout-callback/page.tsx`

**Vấn đề:** Sau khi xử lý logout callback, trang redirect về chính nó thay vì về login page.

**Đã sửa:**
- Thay đổi `router.replace('/auth/keycloak/logout-callback')` thành `router.replace('/auth/login')`
- Thêm cleanup localStorage và sessionStorage
- Cải thiện UI với Material-UI components

### 2. Cấu hình Keycloak Client cần kiểm tra

Đăng nhập vào Keycloak Admin Console và kiểm tra client `test01`:

#### A. Valid Redirect URIs
Phải bao gồm **TẤT CẢ** các URL sau:
```
http://localhost:8082/auth/keycloak/callback
http://localhost:8082/auth/keycloak/logout-callback
http://localhost:8082/*
```

#### B. Web Origins
Phải bao gồm:
```
http://localhost:8082
```

#### C. Settings cần thiết
- **Access Type:** `public`
- **Standard Flow Enabled:** `ON`
- **Direct Access Grants Enabled:** `ON`
- **Valid Post Logout Redirect URIs:** `http://localhost:8082/*`

### 3. Kiểm tra Environment Variables
File `.env` hoặc `.env.local`:
```env
NEXT_PUBLIC_KEYCLOAK_AUTHORITY=http://localhost:8080/realms/ttu
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=test01
NEXT_PUBLIC_KEYCLOAK_REDIRECT_URI=http://localhost:8082/auth/keycloak/callback
NEXT_PUBLIC_KEYCLOAK_POST_LOGOUT_REDIRECT_URI=http://localhost:8082/auth/keycloak/logout-callback
NEXT_PUBLIC_KEYCLOAK_SCOPE=openid profile email
```

## 🔧 Cách kiểm tra và debug

### 1. Truy cập Debug Page
Mở: `http://localhost:8082/auth/keycloak/debug`

Trang này sẽ hiển thị:
- Cấu hình Keycloak hiện tại
- Thông tin user đang đăng nhập
- Nút test logout
- Hướng dẫn troubleshooting

### 2. Kiểm tra Network Tab
Khi logout, kiểm tra trong Browser DevTools > Network:
1. Request đến `/auth/keycloak/logout-callback` phải trả về 200
2. Không có lỗi CORS
3. Redirect cuối cùng phải về `/auth/login`

### 3. Kiểm tra Console Logs
Mở Browser DevTools > Console để xem:
- "Keycloak logout callback successful"
- Không có error messages

## 🚀 Cách test

1. **Đăng nhập:** Truy cập `http://localhost:8082/auth/login`
2. **Đăng xuất:** Click nút logout trong ứng dụng
3. **Kiểm tra:** Phải được redirect về login page, không kẹt ở Keycloak

## 📋 Checklist khắc phục

- [ ] Đã sửa file `logout-callback/page.tsx`
- [ ] Kiểm tra Valid Redirect URIs trong Keycloak client
- [ ] Kiểm tra Web Origins trong Keycloak client  
- [ ] Kiểm tra Access Type = public
- [ ] Kiểm tra environment variables
- [ ] Test logout flow hoàn chỉnh
- [ ] Kiểm tra không có lỗi trong console

## 🆘 Nếu vẫn không hoạt động

1. **Restart Keycloak server**
2. **Clear browser cache và localStorage**
3. **Kiểm tra Keycloak server logs**
4. **Thử với incognito/private browsing mode**
5. **Kiểm tra firewall/proxy settings**

## 📞 Debug URLs

- Debug page: `http://localhost:8082/auth/keycloak/debug`
- Keycloak admin: `http://localhost:8080/admin`
- Keycloak realm: `http://localhost:8080/realms/ttu`
