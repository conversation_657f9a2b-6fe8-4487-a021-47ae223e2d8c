'use client';

import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

export type SignInParams = {
  email?: string;
  password?: string;
};

export type SignUpParams = {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
};

// ----------------------------------------------------------------------

// These are standalone action functions that can be imported directly
// Similar to other auth providers in the codebase

/** **************************************
 * Sign in
 *************************************** */
export const signInWithPassword = async (params?: SignInParams): Promise<void> => {
  // Keycloak handles authentication through redirect
  // params are not used as Keycloak manages the login form
  const { authority, clientId, redirectUri, postLogoutRedirectUri, scope } = CONFIG.keycloak;

  if (!(authority && clientId && redirectUri)) {
    throw new Error('Keycloak configuration is missing');
  }

  // Import UserManager dynamically to avoid SSR issues
  const { UserManager, WebStorageStateStore } = await import('oidc-client-ts');

  const userManager = new UserManager({
    authority,
    client_id: clientId,
    redirect_uri: redirectUri,
    post_logout_redirect_uri: postLogoutRedirectUri,
    response_type: 'code',
    scope,
    automaticSilentRenew: true,
    silent_redirect_uri: `${window.location.origin}/silent-renew.html`,
    userStore: new WebStorageStateStore({ store: window.localStorage }),
    loadUserInfo: true,
  });

  await userManager.signinRedirect();
};

/** **************************************
 * Sign up
 *************************************** */
export const signUp = async (params: SignUpParams): Promise<void> => {
  // Keycloak typically handles registration through its admin console
  // or registration flow. This would redirect to Keycloak's registration page
  throw new Error('Sign up should be handled through Keycloak registration flow');
};

/** **************************************
 * Sign out
 *************************************** */
export const signOut = async (): Promise<void> => {
  const { authority, clientId, redirectUri, postLogoutRedirectUri, scope } = CONFIG.keycloak;

  if (!(authority && clientId && redirectUri)) {
    throw new Error('Keycloak configuration is missing');
  }

  // Import UserManager dynamically to avoid SSR issues
  const { UserManager, WebStorageStateStore } = await import('oidc-client-ts');

  const userManager = new UserManager({
    authority,
    client_id: clientId,
    redirect_uri: redirectUri,
    post_logout_redirect_uri: postLogoutRedirectUri,
    response_type: 'code',
    scope,
    automaticSilentRenew: true,
    silent_redirect_uri: `${window.location.origin}/silent-renew.html`,
    userStore: new WebStorageStateStore({ store: window.localStorage }),
    loadUserInfo: true,
  });

  await userManager.signoutRedirect();
};

/** **************************************
 * Reset password
 *************************************** */
export const resetPassword = async (email: string): Promise<void> => {
  // Keycloak handles password reset through its own flow
  throw new Error('Password reset should be handled through Keycloak forgot password flow');
};

/** **************************************
 * Update password
 *************************************** */
export const updatePassword = async (password: string): Promise<void> => {
  // Password updates are typically handled through Keycloak account management
  throw new Error('Password update should be handled through Keycloak account management');
};

/** **************************************
 * Confirm register
 *************************************** */
export const confirmRegister = async (email: string, code: string): Promise<void> => {
  // Email confirmation is handled by Keycloak
  throw new Error('Email confirmation is handled by Keycloak');
};

/** **************************************
 * Send password reset email
 *************************************** */
export const sendPasswordResetEmail = async (email: string): Promise<void> => {
  // Password reset emails are sent by Keycloak
  throw new Error('Password reset emails are sent by Keycloak');
};
