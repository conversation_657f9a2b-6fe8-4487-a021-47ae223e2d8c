'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

import { useKeycloak } from 'src/auth/context/keycloak';

// ----------------------------------------------------------------------

export default function KeycloakLogoutCallbackPage() {
  const router = useRouter();
  const { signOutCallback } = useKeycloak();

  useEffect(() => {
    const handleLogoutCallback = async () => {
      try {
        await signOutCallback();
        // Redirect to login page after successful logout
        // router.replace('/auth/login');
        router.replace('/auth/keycloak/logout-callback');

      } catch (error) {
        console.error('Keycloak logout callback error:', error);
        // Still redirect to login page even if there's an error
        router.replace('/auth/login');
      }
    };

    handleLogoutCallback();
  }, [signOutCallback, router]);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column'
    }}>
      <div>Signing out...</div>
      <div style={{ marginTop: '20px' }}>
        <div style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '40px',
          height: '40px',
          animation: 'spin 2s linear infinite'
        }} />
      </div>
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
