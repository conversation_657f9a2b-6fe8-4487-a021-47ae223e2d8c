'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

import { CONFIG } from 'src/global-config';

import { useKeycloak } from 'src/auth/context/keycloak';

// ----------------------------------------------------------------------

export default function KeycloakCallbackPage() {
  const router = useRouter();
  const { signInCallback } = useKeycloak();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const user = await signInCallback();
        if (user) {
          // Redirect to the intended page or dashboard
          router.replace(CONFIG.auth.redirectPath);
        } else {
          // Redirect to login page if callback failed
          router.replace('/auth/login');
        }
      } catch (error) {
        console.error('Keycloak callback error:', error);
        router.replace('/auth/login');
      }
    };

    handleCallback();
  }, [signInCallback, router]);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column'
    }}>
      <div>Processing authentication...</div>
      <div style={{ marginTop: '20px' }}>
        <div className="spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '40px',
          height: '40px',
          animation: 'spin 2s linear infinite'
        }} />
      </div>
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
